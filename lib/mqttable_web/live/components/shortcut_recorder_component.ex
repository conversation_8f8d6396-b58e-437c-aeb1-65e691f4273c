defmodule MqttableWeb.ShortcutRecorderComponent do
  @moduledoc """
  A macOS-style shortcut recorder component for capturing keyboard shortcuts.
  """
  use MqttableWeb, :live_component

  @impl true
  def mount(socket) do
    socket =
      socket
      |> assign(:recording, false)
      |> assign(:recorded_shortcut, "")
      |> assign(:display_shortcut, "")

    {:ok, socket}
  end

  @impl true
  def update(assigns, socket) do
    socket =
      socket
      |> assign(assigns)
      |> assign(:display_shortcut, format_shortcut_display(assigns[:value] || ""))

    {:ok, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="shortcut-recorder">
      <div class="form-control">
        <label class="label">
          <span class="label-text font-medium">
            <.icon name="hero-command-line" class="size-4 mr-2 inline" /> Send Message Shortcut
          </span>
        </label>
        
    <!-- Shortcut Display/Recording Area -->
        <div class="relative">
          <div
            class={[
              "input input-bordered w-full h-12 flex items-center justify-center cursor-pointer transition-all duration-200",
              if(@recording, do: "border-primary bg-primary/10", else: "hover:border-base-content/30")
            ]}
            phx-click="start_recording"
            phx-target={@myself}
            id={"shortcut-recorder-#{@id}"}
            phx-hook="ShortcutRecorder"
            data-recording={@recording}
          >
            <%= if @recording do %>
              <div class="flex items-center gap-2 text-primary">
                <div class="loading loading-spinner loading-sm"></div>
                <span class="font-medium">Recording...</span>
                <span class="text-sm opacity-70">Press any key combination</span>
              </div>
            <% else %>
              <div class="flex items-center gap-2">
                <%= if @display_shortcut != "" do %>
                  <div class="flex items-center gap-1">
                    <%= for key <- String.split(@display_shortcut, "+") do %>
                      <kbd class="kbd kbd-sm">{key}</kbd>
                      <%= if key != List.last(String.split(@display_shortcut, "+")) do %>
                        <span class="text-base-content/50">+</span>
                      <% end %>
                    <% end %>
                  </div>
                <% else %>
                  <span class="text-base-content/50">Click to record shortcut</span>
                <% end %>

                <%= if @display_shortcut != "" do %>
                  <button
                    type="button"
                    class="btn btn-ghost btn-xs ml-auto"
                    phx-click="clear_shortcut"
                    phx-target={@myself}
                    title="Clear shortcut"
                  >
                    <.icon name="hero-x-mark" class="size-3" />
                  </button>
                <% end %>
              </div>
            <% end %>
          </div>
          
    <!-- Cancel Recording Button -->
          <%= if @recording do %>
            <button
              type="button"
              class="absolute -top-2 -right-2 btn btn-circle btn-xs btn-error"
              phx-click="cancel_recording"
              phx-target={@myself}
              title="Cancel recording"
            >
              <.icon name="hero-x-mark" class="size-3" />
            </button>
          <% end %>
        </div>

        <label class="label">
          <span class="label-text-alt text-base-content/70">
            Keyboard shortcut to open/close the send message modal. Click to record a new shortcut.
          </span>
        </label>
        
    <!-- Quick Options -->
        <div class="flex flex-wrap gap-2 mt-2">
          <span class="text-xs text-base-content/60">Quick options:</span>
          <%= for option <- get_quick_options() do %>
            <button
              type="button"
              class="btn btn-xs btn-outline"
              phx-click="set_shortcut"
              phx-value-shortcut={option.value}
              phx-target={@myself}
            >
              {option.label}
            </button>
          <% end %>
          <button
            type="button"
            class="btn btn-xs btn-outline btn-error"
            phx-click="disable_shortcut"
            phx-target={@myself}
          >
            Disable
          </button>
        </div>
      </div>
    </div>
    """
  end

  @impl true
  def handle_event("start_recording", _params, socket) do
    socket = assign(socket, :recording, true)
    {:noreply, socket}
  end

  @impl true
  def handle_event("cancel_recording", _params, socket) do
    socket = assign(socket, :recording, false)
    {:noreply, socket}
  end

  @impl true
  def handle_event("clear_shortcut", _params, socket) do
    socket =
      socket
      |> assign(:recording, false)
      |> assign(:display_shortcut, "")

    # Notify parent component
    send(self(), {:shortcut_changed, socket.assigns.name, ""})

    {:noreply, socket}
  end

  @impl true
  def handle_event("set_shortcut", %{"shortcut" => shortcut}, socket) do
    display_shortcut = format_shortcut_display(shortcut)

    socket =
      socket
      |> assign(:recording, false)
      |> assign(:display_shortcut, display_shortcut)

    # Notify parent component
    send(self(), {:shortcut_changed, socket.assigns.name, shortcut})

    {:noreply, socket}
  end

  @impl true
  def handle_event("disable_shortcut", _params, socket) do
    socket =
      socket
      |> assign(:recording, false)
      |> assign(:display_shortcut, "")

    # Notify parent component
    send(self(), {:shortcut_changed, socket.assigns.name, ""})

    {:noreply, socket}
  end

  @impl true
  def handle_event("shortcut_recorded", %{"shortcut" => shortcut}, socket) do
    # This event comes from the JavaScript hook
    display_shortcut = format_shortcut_display(shortcut)

    socket =
      socket
      |> assign(:recording, false)
      |> assign(:display_shortcut, display_shortcut)

    # Notify parent component
    send(self(), {:shortcut_changed, socket.assigns.name, shortcut})

    {:noreply, socket}
  end

  # Private functions

  defp format_shortcut_display(""), do: ""

  defp format_shortcut_display(shortcut) do
    shortcut
    |> String.split("+")
    |> Enum.map(&format_key_name/1)
    |> Enum.join("+")
  end

  defp format_key_name("ctrl"), do: "⌘"
  defp format_key_name("shift"), do: "⇧"
  defp format_key_name("alt"), do: "⌥"
  defp format_key_name("meta"), do: "⌘"
  defp format_key_name(key), do: String.upcase(key)

  defp get_quick_options do
    [
      %{value: "ctrl+k", label: "⌘K"},
      %{value: "ctrl+shift+k", label: "⌘⇧K"},
      %{value: "ctrl+m", label: "⌘M"},
      %{value: "ctrl+n", label: "⌘N"}
    ]
  end
end
