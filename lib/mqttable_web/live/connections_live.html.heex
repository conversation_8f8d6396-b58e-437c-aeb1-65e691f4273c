<!-- Persistent Flash Messages - Always on top -->
<div
  :if={Phoenix.Flash.get(@flash, :error)}
  id="flash-error"
  class="alert alert-error mb-2 mx-2 mt-2 fixed top-4 right-4 z-[99999] w-80 sm:w-96 max-w-80 sm:max-w-96 shadow-lg"
  style="z-index: 99999 !important; position: fixed !important;"
>
  <.icon name="hero-exclamation-circle" class="size-5" />
  <span>{Phoenix.Flash.get(@flash, :error)}</span>
  <button
    type="button"
    class="btn btn-sm btn-ghost"
    phx-click="lv:clear-flash"
    phx-value-key="error"
  >
    ×
  </button>
</div>

<div
  :if={Phoenix.Flash.get(@flash, :info)}
  id="flash-info"
  class="alert alert-info mb-2 mx-2 mt-2 fixed top-4 right-4 z-[99999] w-80 sm:w-96 max-w-80 sm:max-w-96 shadow-lg"
  style="top: 5rem; z-index: 99999 !important; position: fixed !important;"
>
  <.icon name="hero-information-circle" class="size-5" />
  <span>{Phoenix.Flash.get(@flash, :info)}</span>
  <button
    type="button"
    class="btn btn-sm btn-ghost"
    phx-click="lv:clear-flash"
    phx-value-key="info"
  >
    ×
  </button>
</div>

<!-- Unified Panel for all components -->
<div id="main-panel" class={[
  "unified-panel bg-base-100 flex",
  if(
    @active_connection_set &&
      Map.get(@expanded_sets, @active_connection_set.name, "expanded") == "collapsed",
    do: "broker-tabs-collapsed",
    else: ""
  )
]} id="main-container" phx-window-keydown="global_keydown">
  <!-- Icon Menu -->
  <.icon_menu class="panel-icon-menu">
    <:top_item icon="hero-inbox-stack" tooltip="Brokers" active={true} navigate={~p"/"} />
    <:top_item icon="hero-wrench-screwdriver" href="#toolbox" tooltip="Toolbox" />

    <:bottom_item icon="hero-cog-6-tooth" phx_click="open_settings_modal" tooltip="Settings" />

    <:bottom_item icon="hero-user-circle" tooltip="Account" />
  </.icon_menu>
  
<!-- Main Content Area -->
  <div class="flex-1 flex flex-col overflow-y-auto">
    <!-- Broker Tabs -->
    <.live_component
      module={MqttableWeb.BrokerTabsComponent}
      id="broker-tabs"
      connection_sets={@connection_sets}
      active_connection_set={@active_connection_set}
      expanded_sets={@expanded_sets}
    />
    
<!-- Connection Component -->
    <div class="border-t border-base-300">
      <.live_component
        module={MqttableWeb.ConnectionComponent}
        id="connection-component"
        active_connection_set={@active_connection_set}
        connection_sets={@connection_sets}
        expanded_sets={@expanded_sets}
      />
    </div>
    <!-- Trace Grid Component -->
    <div class="border-t border-base-300">
      <.live_component
        module={MqttableWeb.TraceGridComponent}
        id="trace-grid-component"
        active_broker_name={if @active_connection_set, do: @active_connection_set.name, else: nil}
      />
      <!-- Hidden stream container for LiveView streams -->
      <div id="trace-messages-stream" phx-update="stream" style="display: none;">
        <div
          :for={{dom_id, message} <- @streams.trace_messages}
          id={dom_id}
          data-message={Jason.encode!(serialize_message_for_json(message))}
        >
        </div>
      </div>
    </div>
  </div>
  
<!-- Floating Action Button -->
  <div class="absolute bottom-4 right-4 z-50 flex flex-col items-center gap-2">
    <button
      class="btn btn-primary btn-circle btn-lg shadow-lg hover:shadow-xl transition-shadow"
      phx-click="open_send_modal"
      title="Send MQTT Message"
    >
      <.icon name="hero-paper-airplane" class="size-6" />
    </button>

    <!-- Keyboard Shortcut Hint -->
    <%= if Mqttable.Settings.get_send_modal_shortcut() != "" do %>
      <div class="flex items-center gap-1 text-xs text-base-content/60 bg-base-200/80 backdrop-blur-sm px-2 py-1 rounded-lg shadow-sm">
        <%= for {key, index} <- format_shortcut_display(Mqttable.Settings.get_send_modal_shortcut()) |> Enum.with_index() do %>
          <%= if index > 0 do %>
            <span class="text-[10px]">+</span>
          <% end %>
          <kbd class="kbd kbd-xs">
            <span class="text-[10px]">{key}</span>
          </kbd>
        <% end %>
      </div>
    <% end %>
  </div>

  <script>
    // Update shortcut key display based on platform
    document.addEventListener('DOMContentLoaded', function() {
      const shortcutKey = document.getElementById('shortcut-key');
      if (shortcutKey && navigator.platform.indexOf('Mac') > -1) {
        shortcutKey.textContent = '⌘';
      }
    });
  </script>
</div>

<%= if @show_modal do %>
  <.modal id="connection-set-modal" show on_cancel={JS.push("close_modal")}>
    <%= case @modal_type do %>
      <% :new_connection_set -> %>
        <.live_component
          module={MqttableWeb.NewConnectionSetModalComponent}
          id="new-connection-set-modal"
          edit_connection_set={@edit_connection_set}
          connection_sets={@connection_sets}
        />
      <% :edit_connection_set -> %>
        <.live_component
          module={MqttableWeb.EditConnectionSetModalComponent}
          id="edit-connection-set-modal"
          edit_connection_set={@edit_connection_set}
          connection_sets={@connection_sets}
        />
      <% :edit_connection -> %>
        <.live_component
          module={MqttableWeb.EditConnectionModalComponent}
          id="edit-connection-modal"
          edit_connection={@edit_connection}
          connection_set={@connection_set}
        />
      <% :new_connection -> %>
        <.live_component
          module={MqttableWeb.NewConnectionModalComponent}
          id="new-connection-modal"
          connection_set={@connection_set}
          edit_connection={@edit_connection}
        />
    <% end %>
  </.modal>
<% end %>

<%= if @show_subscription_modal do %>
  <.live_component
    module={MqttableWeb.SubscriptionModalComponent}
    id="subscription-modal"
    active_connection_set={@active_connection_set}
    pre_selected_client_id={@pre_selected_client_id}
    edit_mode={@edit_mode}
    client_id={@client_id}
    topic={@topic}
    qos={@qos}
    nl={@nl}
    rap={@rap}
    rh={@rh}
    sub_id={@sub_id}
    index={@index}
  />
<% end %>

<%= if @show_scheduled_message_modal do %>
  <.live_component
    module={MqttableWeb.ScheduledMessageModalComponent}
    id="scheduled-message-modal"
    show_modal={@show_scheduled_message_modal}
    active_connection_set={@active_connection_set}
    active_broker_name={if @active_connection_set, do: @active_connection_set.name, else: nil}
    pre_selected_client_id={@pre_selected_client_id}
    edit_mode={@scheduled_message_edit_mode}
    edit_index={@scheduled_message_edit_index}
    scheduled_message={@scheduled_message_data}
  />
<% end %>

<!-- Send Message Modal Component -->
<.live_component
  module={MqttableWeb.SendMessageModalComponent}
  id="send-message-modal"
  show_modal={@show_send_modal}
  active_broker_name={if @active_connection_set, do: @active_connection_set.name, else: nil}
  form_state={@send_modal_form_state}
/>

<!-- Message Detail Modal Component -->
<.live_component
  module={MqttableWeb.MessageDetailModalComponent}
  id="message-detail-modal"
  show_modal={@show_detail_modal}
  message={@detail_modal_message}
  payload_view_type={@payload_view_type}
/>

<!-- Settings Modal -->
<%= if @show_settings_modal do %>
  <.modal id="settings-modal" show on_cancel={JS.push("close_settings_modal")}>
    <.live_component module={MqttableWeb.SettingsModalComponent} id="settings-modal-component" />
  </.modal>
<% end %>

<!-- Template Manager Modal -->
<%= if @show_template_manager do %>
  <.live_component
    module={MqttableWeb.TemplateManagerModalComponent}
    id="template-manager-modal"
    show_modal={@show_template_manager}
  />
<% end %>
